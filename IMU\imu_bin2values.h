#ifndef IMU_BIN2VALUES_H
#define IMU_BIN2VALUES_H

#include "xparameters.h"
#include "xplatform_info.h"
#include "xuartps.h"
#include "xil_exception.h"
#include "xil_printf.h"
#include "xscugic.h"

#define TEST_BUFFER_SIZE	64

typedef struct {
	double Ins_Head;
	double Ins_Pit;
	double Ins_Roll;


}Ins_xyz;
typedef struct {
	float Ins_latitude;
	float	Ins_Longitude;
	float Ins_Height;


}Ins_gps;
typedef struct
{
	u16 Gps_Week;
	double Gps_Wecond;
	float Ins_Head_Angle;
	float Ins_Pitch_Angle;
	float Ins_Roll_Angle;
	float Ins_latitude;
	float	Ins_Longitude;
	float Ins_Height;
	float Ins_Eastbound_speed;
	float Ins_Northbound_speed;
	float Ins_Celestial_velocity;
	float Ins_Baseline_length;
	u8 	  Ins_Ant_1_Count;
	u8	  Ins_Ant_2_Count;
	u8	  Ins_Sate;

}Ins_1;

typedef struct
{
	u16 Gps_Week;
	double Gps_Wecond;
	double Ins_GyroX;
	double Ins_GyroY;
	double Ins_GyroZ;
	double Ins_AccX;
	double Ins_AccY;
	double Ins_AccZ;
	float  Ins_Temp;

}Ins_5;


void Dev_Ins_Handle();

Ins_xyz Dev_Ins_Gethpr(void);
Ins_gps Dev_Ins_Getgps(void);
u32 FloatToU32(float dat);

float U32ToFloat(u32 dat);

u8 *IMU_GetInsBuffer(void);

#endif /* IMU_BIN2VALUES_H */
