#include <stdint.h>
#include <stdio.h>
//#include "xttcps.h"
#include "GlobalTimer.h"
#include "../FeatureCtrl.h"
#include "../MCU/EBSA_task.h"
// Timer Instance
XScuTimer Timer;
XTtcPs ttc_timer;    // TTC定时器实例
// 计数器,用于追踪0.5ms中断次数
static volatile uint32_t InterruptCount = 0;
static volatile uint32_t MoniterTaskCount = 0;
XScuTimer *GetTimerInst()
{
	return &Timer;
}

XTtcPs *GetTtcInst()
{
	return &ttc_timer;
}

TimeInfo *GPST2UTC_YMD(uint16_t gps_week, uint32_t gps_ms)
{
    

}




extern	SemaphoreHandle_t Debug_sem;
// TTC中断处理函数
void TtcInterruptHandler(void *CallBackRef)
{
    uint32_t StatusEvent;
    XTtcPs *TtcInstance = (XTtcPs *)CallBackRef;

    // 获取中断状态
    StatusEvent = XTtcPs_GetInterruptStatus(TtcInstance);

    // 清除中断状态
    XTtcPs_ClearInterruptStatus(TtcInstance, StatusEvent);

    // 增加中断计数
    InterruptCount++;
#ifdef SYS_DEBUG_MONITER
    if(MoniterTaskCount < 20*499)
		MoniterTaskCount++;
	else
	{
	    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
//	    xTaskNotifyFromISR(xDebugTaskHandle, 1, eNoAction, &xHigherPriorityTaskWoken);
	    xSemaphoreGiveFromISR(Debug_sem, &xHigherPriorityTaskWoken);
//		xTaskNotifyFromISR(xDebugTaskHandle, DEBUG_NOTIFY_1, eSetBits);

		MoniterTaskCount = 0;

	}
#endif

    // 每20次0.5ms中断(即10ms)执行一次主任务
    if(InterruptCount >= 20) {
        InterruptCount = 0;
        // 在这里添加您的10ms主任务
        EBSA_SendCommand(EBSA_GetManager(),CMD_START_CAL);
    }
}

// TTC初始化函数
int init_ttc_timestamp(void) {
    XTtcPs_Config *config;
    int status;

    // 获取TTC配置
    config = XTtcPs_LookupConfig(XPAR_PS7_TTC_0_DEVICE_ID);  // 使用TTC0
    if (NULL == config) {
        return XST_FAILURE;
    }

    // 初始化TTC
    status = XTtcPs_CfgInitialize(&ttc_timer, config, config->BaseAddress);
    if (status != XST_SUCCESS) {
        return status;
    }

    // 停止定时器进行配置
    XTtcPs_Stop(&ttc_timer);

//    // 配置为自由运行模式,使用内部时钟源(XPAR_XTTCPS_1_TTC_CLK_CLKSRC为0表示内部时钟)
//    XTtcPs_SetOptions(&ttc_timer, XTTCPS_OPTION_DECREMENT |
//                                 XTTCPS_OPTION_WAVE_DISABLE|
//								 XTTCPS_OPTION_INTERVAL_MODE);
//
////    // 不分频,直接使用111.11MHz时钟
////    XTtcPs_SetPrescaler(&ttc_timer, XTTCPS_CLK_CNTRL_PS_DISABLE);
////    XTtcPs_SetInterval(&ttc_timer, 0xFFFF);//16 bit // 16位最大值
    // 设置为间隔模式
    XTtcPs_SetOptions(&ttc_timer, XTTCPS_OPTION_INTERVAL_MODE);

    // 计算0.5ms所需的计数值
    // TTC时钟频率为111.111115MHz
    // 0.5ms = 500us = (111111115 * 0.0005) = 55555.5575个时钟周期
    uint32_t Interval = 55556; // 四舍五入到整数
    XTtcPs_SetInterval(&ttc_timer, Interval);

    // 启用间隔中断
    XTtcPs_EnableInterrupts(&ttc_timer, XTTCPS_IXR_INTERVAL_MASK);

    //GIC中放开
//    XTtcPs_Start(&ttc_timer);


    return XST_SUCCESS;
}

// 获取时间戳函数
uint16_t  get_ttc_timestamp(void) {
    return XTtcPs_GetCounterValue(&ttc_timer);
}

// 计算时间差(单位:微秒)
uint16_t  get_time_difference_us(uint16_t  start, uint16_t  end)
{
	uint16_t  ticks;
    if (end >= start) {
        ticks = end - start;
    } else {
        ticks = (0xFFFFFFFF - start) + end + 1;
    }

    // 转换为微秒: ticks * (1000000 / 111111115)
    return (uint16_t )((float)ticks * 9.0f);
}

XTime GetTimeDiff(XTime currentTime, XTime lastTime)
{
    if(currentTime >= lastTime) {
        return currentTime - lastTime;
    } else {
        // 处理计数器溢出的情况
        return (0xFFFFFFFFFFFFFFFF - lastTime) + currentTime + 1;
    }
}

// 计算时间差(单位:毫秒)
u32  get_time_difference_ms(XTime  start, XTime  end)
{
	u32 timeDiff = GetTimeDiff(end, start)/COUNTS_PER_MS;
    return timeDiff;
}

// 定时器中断处理函数
void Timer_IRQHandler(void *CallBackRef)
{
    XScuTimer *timer_inst = (XScuTimer *)CallBackRef;

    // 清除中断标志
    XScuTimer_ClearInterruptStatus(timer_inst);
}

// 初始化硬件定时器
int Timer_Init(void)
{
    XScuTimer_Config *timer_config;

    // 获取定时器配置
    timer_config = XScuTimer_LookupConfig(XPAR_XSCUTIMER_0_DEVICE_ID);
    if (timer_config == NULL) {
        return XST_FAILURE;
    }

    // 初始化定时器
    if (XScuTimer_CfgInitialize(&Timer, timer_config, timer_config->BaseAddr) != XST_SUCCESS) {
        return XST_FAILURE;
    }

    // 配置定时器
    XScuTimer_LoadTimer(&Timer, TIMER_FULL_VALUE);
    XScuTimer_EnableAutoReload(&Timer);

/*
 * XScuTimer_EnableInterrupt(&Timer);      // 使能定时器中断

     // 4. 初始化定时器中断
     if (Timer_IntInit(manager) != XST_SUCCESS) {
         return XST_FAILURE;
     }

     */

    // 启动定时器
    XScuTimer_Start(&Timer);

    return XST_SUCCESS;
}

// 获取时间间隔，单位：秒
double GetTimeInterval(uint32_t count1, uint32_t count2) {
    uint32_t deltaCount;
    if (count2 <= count1) {
        deltaCount = count1 - count2;
    } else {
        deltaCount = count1 + (TIMER_FULL_VALUE - count2 + 1);
    }
    return ((double)deltaCount) / (double)TIMER_FREQ_HZ;
}


// 直接读取计数值
uint32_t ReadPrivateTimerCounter() {
    return *((volatile uint32_t *)TIMER_COUNTER_REG);
}
