#ifndef	EMMC_H
#define EMMC_H

typedef struct {
	uint8_t 	type ;
	uint8_t		channal[2];
	int8_t 		tempra;
	uint8_t 	freqpoint ;
	uint8_t		jihua;
	uint8_t 	betalist[1024];
	uint8_t 	alfalist[1024];
}pldataBstruct;


int InitEmmc(void);
int emmc_read(uint32_t address, uint8_t *buffer, uint32_t length);
int emmc_write(uint32_t address, const uint8_t *data, uint32_t length) ;
int WriteCompenTable(u8 *data, u32 length);
int CompenTableRead(void);
int EMMC_EraseAll();
#endif
