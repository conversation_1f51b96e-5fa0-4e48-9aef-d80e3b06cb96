#ifndef __INCLUDE_H_
#define __INCLUDE_H_

#include <stdio.h>
#include "xil_printf.h"

#include "xparameters.h"
#include "xplatform_info.h"
#include "xuartps.h"
#include "xil_exception.h"
#include "xil_printf.h"
#include "xscugic.h"
#include <math.h>
#include "stdio.h"
#include "stdlib.h"
#include "string.h"

#include "complex.h"


#include "xqspips.h"
//#include "../lwip/err.h"
//#include "lwip/tcp.h"
//#include "lwipopts.h"
//#include "netif/xadapter.h"
//#include "lwipopts.h"
#include "xil_cache.h"
#include "xparameters.h"
#include "xparameters_ps.h"	/* defines XPAR values */
#include "xil_cache.h"
#include "xscugic.h"
#include "xil_printf.h"
/*
#include "platform_config.h"
#include "netif/xadapter.h"
#ifdef PLATFORM_ZYNQ
#include "xscutimer.h"

*/
//#include <stdio.h>
/*
#include "xadcps.h"
#include "xil_types.h"
#include "Xscugic.h"
#include "Xil_exception.h"
*/
//#include "xscutimer.h"

//static  netif *netif, server_netif;

#endif
